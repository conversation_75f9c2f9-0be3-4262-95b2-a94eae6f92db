{"name": "Property-Rental", "version": "0.1.0", "private": true, "dependencies": {"@rainbow-me/rainbowkit": "^2.2.8", "@sendgrid/mail": "^8.1.3", "@tanstack/react-query": "^5.90.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "bcryptjs": "^2.4.3", "bootstrap": "^5.3.2", "cloudinary": "^2.3.0", "concurrently": "^8.2.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-fileupload": "^1.5.1", "framer-motion": "^10.16.4", "git": "^0.1.5", "jsonwebtoken": "^9.0.2", "mongoose": "^8.5.1", "mongoose-url-slugs": "^1.0.2", "paytmchecksum": "^1.5.1", "react": "^18.2.0", "react-bootstrap": "^2.9.0", "react-dom": "^18.2.0", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "request": "^2.88.2", "sqlite3": "^5.1.7", "swiper": "^10.3.1", "validator": "^13.12.0", "viem": "^2.38.0", "wagmi": "^2.17.5", "web-vitals": "^2.1.4"}, "scripts": {"predeploy": "concurrently \"node server/server.js\" \"npm run build\"", "deploy": "concurrently \"node server/server.js\" \"gh-pages -d build\"", "start": "concurrently \"node server/server.js\" \"react-scripts start\"", "build": "concurrently \"node server/server.js\" \"react-scripts build\"", "test": "concurrently \"node server/server.js\" \"react-scripts test\"", "eject": "concurrently \"node server/server.js\" \"react-scripts eject\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "assert": "^2.1.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "gh-pages": "^6.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.4", "util": "^0.12.5"}}