import Container from "react-bootstrap/Container";
import Nav from "react-bootstrap/Nav";
import Navbar from "react-bootstrap/Navbar";
import logo from "../../images/logo/logo.png";
import {ConnectButton} from "@rainbow-me/rainbowkit";
import {useWallet} from "../../hooks/useWallet";
import "bootstrap/dist/css/bootstrap.min.css";
import "./navbar.css";

function NavBar() {
  const {
    isConnected,
    isConnecting,
    formatAddress,
    networkName,
    isMetaMaskInstalled,
    connectionError,
  } = useWallet();

  return (
    <Navbar
      expand="lg"
      className="py-3"
    >
      <Container>
        <Navbar.Brand
          href="#"
          className="me-lg-5"
        >
          <img
            className="logo"
            src={logo}
            alt="logo"
          />
        </Navbar.Brand>
        <Navbar.Toggle aria-controls="navbarScroll" />
        <Navbar.Collapse id="navbarScroll">
          <Nav
            className="me-auto my-2 my-lg-0"
            navbarScroll
          >
            <Nav.Link href="#action1">Marketplace</Nav.Link>
            <Nav.Link
              href="#action2"
              className="px-lg-3"
            >
              About Us
            </Nav.Link>
            <Nav.Link href="#action3">Developers</Nav.Link>
          </Nav>
        </Navbar.Collapse>
        <div className="d-flex align-items-center order">
          <span className="line d-lg-inline-block d-none"></span>
          <i className="fa-regular fa-heart"></i>

          {/* Wallet Connection Section */}
          <div className="wallet-section d-none d-lg-flex align-items-center">
            {!isMetaMaskInstalled && !isConnected && (
              <div className="metamask-warning me-3">
                <small className="text-warning">
                  Please install MetaMask to connect your wallet
                </small>
              </div>
            )}

            {connectionError && (
              <div className="connection-error me-3">
                <small className="text-danger">{connectionError}</small>
              </div>
            )}

            {isConnecting && (
              <div className="connecting-status me-3">
                <small className="text-info">
                  <i className="fa fa-spinner fa-spin me-1"></i>
                  Connecting...
                </small>
              </div>
            )}

            {isConnected && (
              <div className="wallet-info me-3">
                <small className="text-muted d-block">
                  Connected to {networkName}
                </small>
                <small className="text-primary">{formatAddress()}</small>
              </div>
            )}

            <ConnectButton />
          </div>

          {/* Mobile wallet button */}
          <div className="d-lg-none">
            <ConnectButton />
          </div>
        </div>
      </Container>
    </Navbar>
  );
}

export default NavBar;
