import {getDefaultConfig} from "@rainbow-me/rainbowkit";
import {
  mainnet,
  polygon,
  optimism,
  arbitrum,
  base,
  sepolia,
} from "wagmi/chains";

export const config = getDefaultConfig({
  appName: "Property Rental DApp",
  projectId:
    process.env.REACT_APP_WALLETCONNECT_PROJECT_ID ||
    "c4f79cc821944d9680842e34466bfbd",
  chains: [
    mainnet,
    polygon,
    optimism,
    arbitrum,
    base,
    ...(process.env.NODE_ENV === "development" ? [sepolia] : []),
  ],
  ssr: false,
  // Add wallet connection options for better MetaMask support
  walletConnectParameters: {
    qrModalOptions: {
      themeMode: "light",
      themeVariables: {
        "--wcm-z-index": "1000",
      },
    },
  },
});
