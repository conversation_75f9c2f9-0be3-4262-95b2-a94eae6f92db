import {useEffect, useState} from "react";
import {useAccount, useChainId, useDisconnect, useConnect} from "wagmi";

export const useWallet = () => {
  const {address, isConnected, isConnecting, isDisconnected} = useAccount();
  const chainId = useChainId();
  const {disconnect} = useDisconnect();
  const {connect, connectors, error, isLoading, pendingConnector} =
    useConnect();
  const [previousAddress, setPreviousAddress] = useState(null);
  const [previousChainId, setPreviousChainId] = useState(null);
  const [connectionError, setConnectionError] = useState(null);

  // Handle account changes
  useEffect(() => {
    if (address && address !== previousAddress) {
      if (previousAddress) {
        console.log("Account changed from", previousAddress, "to", address);
        // You can add custom logic here for account changes
        // For example, refresh user data, clear cache, etc.
      }
      setPreviousAddress(address);
    }
  }, [address, previousAddress]);

  // Handle network changes
  useEffect(() => {
    if (chainId && chainId !== previousChainId) {
      if (previousChainId) {
        console.log("Network changed from", previousChainId, "to", chainId);
        // You can add custom logic here for network changes
        // For example, update contract addresses, refresh data, etc.
      }
      setPreviousChainId(chainId);
    }
  }, [chainId, previousChainId]);

  // Handle connection errors
  useEffect(() => {
    if (error) {
      console.error("Wallet connection error:", error);
      setConnectionError(error.message || "Failed to connect wallet");
      // Clear error after 5 seconds
      const timer = setTimeout(() => {
        setConnectionError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  // Clear connection error when successfully connected
  useEffect(() => {
    if (isConnected) {
      setConnectionError(null);
    }
  }, [isConnected]);

  // Format address for display (show first 6 and last 4 characters)
  const formatAddress = (addr) => {
    if (!addr) return "";
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  // Get network name
  const getNetworkName = (id) => {
    const networks = {
      1: "Ethereum",
      137: "Polygon",
      10: "Optimism",
      42161: "Arbitrum",
      8453: "Base",
      11155111: "Sepolia",
    };
    return networks[id] || `Chain ${id}`;
  };

  // Check if MetaMask is installed
  const isMetaMaskInstalled = () => {
    return (
      typeof window !== "undefined" && typeof window.ethereum !== "undefined"
    );
  };

  // Manual connection function for MetaMask
  const connectMetaMask = async () => {
    try {
      setConnectionError(null);
      const metaMaskConnector = connectors.find(
        (connector) => connector.name === "MetaMask"
      );
      if (metaMaskConnector) {
        await connect({connector: metaMaskConnector});
      } else {
        throw new Error("MetaMask connector not found");
      }
    } catch (err) {
      console.error("Failed to connect MetaMask:", err);
      setConnectionError(err.message || "Failed to connect MetaMask");
    }
  };

  return {
    address,
    isConnected,
    isConnecting: isConnecting || isLoading,
    isDisconnected,
    chainId,
    disconnect,
    connect: connectMetaMask,
    connectors,
    connectionError,
    formatAddress: () => formatAddress(address),
    networkName: getNetworkName(chainId),
    isMetaMaskInstalled: isMetaMaskInstalled(),
  };
};
